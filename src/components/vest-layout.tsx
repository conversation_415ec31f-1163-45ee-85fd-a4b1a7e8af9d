import { useContext, useEffect, useState } from 'react';

import { Membership, Round } from '@/class/interface/presale';
import { MembershipsContext } from '@/providers/membership-provider';
import { OldMembershipsContext } from '@/providers/old-membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';

import BuyTokensPanel from './buy-tokens-panel/buy-tokens-panel';
import { Spinner } from './icons/spinner';
import AddToMetamask from './left-panel/add-to-metamask';
import MigrateButton from './left-panel/migrate-button';
import { MembershipSelect } from './left-panel/membership-select';
import RoundDescription from './left-panel/round-description';
import { RoundSelect } from './left-panel/round-select';
import RoundTimings from './left-panel/round-timings';
import { TransferMembership } from './left-panel/transfer-membership';
import NoMemberships from './no-memberships';
import { But<PERSON> } from './ui/button';
import VestPanel from './vest-panel/vest-panel';

// TabPanel component for switching between Vest and Buy panels
interface TabPanelProps {
  selectedMembership: Membership;
  selectedRound: Round;
}

// Tab component for switching between panels
function TabPanel({ selectedMembership, selectedRound }: TabPanelProps) {
  const [activeTab, setActiveTab] = useState<'vest' | 'buy'>('buy');

  return (
    <div className="space-y-6">
      {/* Tab buttons */}
      <div className="flex space-x-2 border-b-2 border-border/20 pb-1">
        <Button
          variant={activeTab === 'buy' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('buy')}
          className="px-10 py-4 rounded-t-xl rounded-b-none border-b-0 text-xl font-bold transition-all duration-200 hover:scale-105"
        >
          💰 Purchase Tokens
        </Button>
        <Button
          variant={activeTab === 'vest' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('vest')}
          className="px-10 py-4 rounded-t-xl rounded-b-none border-b-0 text-xl font-bold transition-all duration-200 hover:scale-105"
        >
          🔒 Claim & Vest
        </Button>
      </div>

      {/* Tab content */}
      <div className="mt-6">
        {activeTab === 'vest' && (
          <VestPanel roundData={selectedRound} membershipData={selectedMembership} />
        )}
        {activeTab === 'buy' && (
          <BuyTokensPanel membership={selectedMembership} round={selectedRound} />
        )}
      </div>
    </div>
  );
}

export default function VestLayout() {
  // Contexts
  const { vestedToken } = useContext(ProjectConfigContext);

  const { presaleData, selectedRoundId, handleRoundChange } = useContext(PresaleContext);

  const { memberships, selectedMembershipId, handleMembershipChange, loading } =
    useContext(MembershipsContext);

  const { memberships: _oldMemberships, loading: _oldLoading } = useContext(OldMembershipsContext);

  const [presaleRounds, setPresaleRounds] = useState(
    presaleData?.rounds.filter((round) =>
      memberships.some((membership) => membership.roundId === round.roundId),
    ) ?? [],
  );

  const [selectedRound, setSelectedRound] = useState(
    presaleRounds.find((round) => round.roundId === selectedRoundId),
  );
  const [roundMemberships, setRoundMemberships] = useState(
    memberships.filter((membership) => membership.roundId === selectedRoundId),
  );
  const [selectedMembership, setSelectedMembership] = useState(
    memberships.find((membership) => membership.id === selectedMembershipId),
  );

  useEffect(() => {
    const _presaleRounds =
      presaleData?.rounds.filter((round) =>
        memberships.some((membership) => membership.roundId === round.roundId),
      ) ?? [];

    // Auto-select first round and membership if none selected and memberships exist
    let currentRoundId = selectedRoundId;
    let currentMembershipId = selectedMembershipId;

    if (memberships.length > 0 && (!selectedRoundId || !selectedMembershipId)) {
      currentRoundId = memberships[0].roundId;
      currentMembershipId = memberships[0].id;
      handleRoundChange(currentRoundId);
      handleMembershipChange(currentMembershipId);
    }

    const _selectedRound = _presaleRounds.find(
      (round) => round.roundId === currentRoundId,
    );
    const _roundMemberships = memberships.filter(
      (membership) => membership.roundId === currentRoundId,
    );

    const _selectedMembership = memberships.find(
      (membership) => membership.id === currentMembershipId,
    );

    setPresaleRounds(_presaleRounds);
    setSelectedRound(_selectedRound);
    setRoundMemberships(_roundMemberships);
    setSelectedMembership(_selectedMembership);


  }, [memberships, selectedRoundId, selectedMembershipId]);

  return !presaleData || loading ? (
    <Spinner className="animate-spin w-12 h-12" />
  ) : (
    <div className="xl:grid xl:grid-cols-3 space-y-4 gap-8 w-full my-8">
      <div className="space-y-12">
        <h2 className="text-3xl sm:text-5xl font-semibold flex flex-col items-start gap-2">
          <span className="text-secondary">{vestedToken.name}</span>
          <span>Vest Portal</span>
        </h2>

        <div className="flex flex-col gap-2">
          <MigrateButton />
        </div>

        <div className="flex flex-col gap-4">
          {presaleRounds.length > 0 && (
            <RoundSelect
              rounds={presaleRounds}
              selectedRoundId={selectedRoundId}
              setSelectedRoundId={(value) => {
                handleRoundChange(value);
                handleMembershipChange(memberships.find((m) => m.roundId === value)!.id);
              }}
            />
          )}

          {roundMemberships.length > 1 && (
            <MembershipSelect
              memberships={roundMemberships}
              selectedMembershipId={selectedMembershipId}
              setSelectedMembershipId={(value) => handleMembershipChange(value)}
            />
          )}

          {roundMemberships.length > 0 && !selectedMembership?.proofs && (
            <TransferMembership />
          )}
        </div>

        {selectedRound && selectedMembership && roundMemberships.length > 0 && (
          <RoundDescription
            round={selectedRound}
            membership={selectedMembership}
            vestedToken={vestedToken}
          />
        )}

        {selectedRound && selectedMembership && roundMemberships.length > 0 && (
          <div className="flex flex-col gap-2">
            <RoundTimings round={selectedRound} membership={selectedMembership} />
          </div>
        )}

        <div className="flex flex-col gap-2">
          <AddToMetamask />
        </div>
      </div>

      <div className="col-span-2">
        {memberships.length <= 0 && loading && (
          <div className="p-4 border border-blue-500 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Loading Allocation Data...</h3>
            <p className="text-blue-700">
              Fetching your allocation information from the blockchain. This may take a moment due to network conditions.
            </p>
          </div>
        )}
        {memberships.length <= 0 && !loading && <NoMemberships />}

        {selectedMembership && selectedRound && (
          <TabPanel selectedMembership={selectedMembership} selectedRound={selectedRound} />
        )}

      </div>
    </div>
  );
}
