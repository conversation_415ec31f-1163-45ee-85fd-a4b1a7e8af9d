import {
  Address,
  Chain,
  Client,
  encodeAbiParameters,
  erc20Abi,
  getContract,
  keccak256,
  WalletClient,
} from 'viem';

import { membershipV5Abi } from '@/abi/membership/membership-v5-abi';
import { presaleV5Abi } from '@/abi/presale/presale-v5-abi';

import {
  IPresale,
  Membership,
  Presale,
  PresaleRoundState,
  PresaleRoundStateValue,
  Round,
} from './interface/presale';
import { PresaleVersion } from './presale-factory';

type GetRoundsReturnType = [number[], Round[], PresaleRoundStateValue[]];

interface PresaleV5Props {
  client: Client;
  contractAddress: Address;
  vestedToken: Address;
  collectedToken: Address;
  listingTimestamp: bigint;
  claimbackPeriod: bigint;
  rounds: Round[];
  membership: Address;
}

interface PresaleV5Proofs {
  address: Address;
  price: string;
  allocation: string;
  claimbackPeriod: number;
  tradeable: number;
  tgeNumerator: number;
  tgeDenominator: number;
  cliffDuration: number;
  cliffNumerator: number;
  cliffDenominator: number;
  vestingPeriodCount: number;
  vestingPeriodDuration: number;
  proofs: string[];
}

interface PresaleV5Membership extends Membership {
  tradeable: 1 | 2;
  claimbackPeriod: number;
}

export class PresaleV5 implements IPresale {
  public getVersion(): PresaleVersion {
    return 'v5';
  }

  public client: Client;

  public presaleContractAddress: Address;

  /// ERC20 implementation of the token sold.
  public vestedToken: Address;

  /// ERC20 implementation of the token collected.
  public collectedToken: Address;

  /// Timestamp indicating when the tge should be available.
  public listingTimestamp: bigint;

  /// How much time in seconds since `listingTimestamp` do Users have to claimback collectedToken
  public claimbackPeriod: bigint;

  /// Presale rounds settings
  public rounds: Round[];

  public membership: Address;

  constructor({
    client,
    contractAddress,
    vestedToken,
    collectedToken,
    listingTimestamp,
    claimbackPeriod,
    rounds,
    membership,
  }: PresaleV5Props) {
    this.client = client;

    this.presaleContractAddress = contractAddress;

    this.vestedToken = vestedToken;
    this.collectedToken = collectedToken;

    this.listingTimestamp = listingTimestamp;
    this.claimbackPeriod = claimbackPeriod;

    this.rounds = rounds;

    this.membership = membership;
  }

  public static async createInstance(
    publicClient: Client,
    presaleContractAddress: Address,
  ): Promise<IPresale> {
    const presaleContract = getContract({
      client: publicClient,
      address: presaleContractAddress,
      abi: presaleV5Abi,
    });

    const [
      vestedToken,
      collectedToken,
      rawRoundsData,
      membership,
      listingTimestamp,
      claimbackPeriod,
    ] = await Promise.all([
      presaleContract.read.tokenA() as Promise<Address>,
      presaleContract.read.tokenB() as Promise<Address>,
      presaleContract.read.getRounds() as unknown as Promise<GetRoundsReturnType>,
      presaleContract.read.membership() as Promise<Address>,
      presaleContract.read.getListingTimestamp() as Promise<bigint>,
      presaleContract.read.claimbackPeriod() as Promise<bigint>,
    ]);

    const [roundIds, roundsData, roundStates] = rawRoundsData;

    const rounds: Round[] = roundIds.map((roundId, index) => {
      const round = roundsData[index];
      const state = roundStates[index];

      return {
        roundId: Number(roundId),
        state,
        name: round.name,
        startTimestamp: Number(round.startTimestamp),
        endTimestamp: Number(round.endTimestamp),
        listingTimestamp: Number(listingTimestamp),
        refundsEndTimestamp: Number(listingTimestamp) + Number(claimbackPeriod),
        proofsUri: round.proofsUri,
        whitelistRoot: round.whitelistRoot,
      };
    });

    return new PresaleV5({
      client: publicClient,
      contractAddress: presaleContractAddress,
      vestedToken,
      collectedToken,
      listingTimestamp,
      claimbackPeriod,
      rounds,
      membership,
    });
  }

  public getPresaleData(): Presale {
    return {
      presaleContractAddress: this.presaleContractAddress,
      vestedToken: this.vestedToken,
      collectedToken: this.collectedToken,
      listingTimestamp: Number(this.listingTimestamp),
      claimbackPeriod: Number(this.claimbackPeriod),
      rounds: this.rounds,
    };
  }

  public async getMemberships(walletAddress: Address): Promise<PresaleV5Membership[]> {
    const memberships: PresaleV5Membership[] = [];

    const proofsMemberships = await this.getProofsMemberships(walletAddress);
    const nftMemberships = await this.getNftMemberships(walletAddress);

    // Get all round IDs that have NFT memberships
    const nftRoundIds = new Set(nftMemberships.map(m => m.roundId));

    // Add proof-based memberships only if there's no NFT membership for the same round
    const filteredProofsMemberships = proofsMemberships.filter(
      proofMembership => !nftRoundIds.has(proofMembership.roundId)
    );

    if (filteredProofsMemberships.length > 0) {
      memberships.push(...filteredProofsMemberships);
    }

    // Always add NFT memberships (they have actual usage tracking)
    if (nftMemberships.length > 0) {
      memberships.push(...nftMemberships);
    }

    return memberships;
  }

  private async getProofsMemberships(
    walletAddress: Address,
  ): Promise<PresaleV5Membership[]> {
    const activeRounds = this.rounds.filter(
      (round) => round.state !== PresaleRoundState.vesting,
    );

    const getProofsForTier = async (round: string, tier: number, address: Address) => {
      const url = `/proofs/${round.toLowerCase()}/new/${tier}/${address.toLowerCase()}.json`;

      const proofs = fetch(url)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          return response.json();
        })
        .catch(() => {
          // Silently handle missing proof files
          return;
        });

      return proofs;
    };

    const proofsPromises = activeRounds.map((round) => {
      const tiers = Array.from({ length: 3 }, (_, i) => i + 1);
      const promises = tiers.map((tier) => {
        return getProofsForTier(round.name, tier, walletAddress);
      });

      return Promise.all(promises);
    });

    const proofs: { [key: number]: PresaleV5Proofs[] } =
      await Promise.all(proofsPromises);

    const memberships: PresaleV5Membership[] = [];

    for (const [index, round] of activeRounds.entries()) {
      for (const [tier, proof] of proofs[index].entries()) {
        if (proof) {
          const _membership: PresaleV5Membership = {
            id: `${round.name} Tier ${tier < 2 ? tier + 1 : '3-5'}`,
            roundId: round.roundId,
            usage: {
              max: '0',
              current: '0',
            },
            price: proof.price,
            allocation: proof.allocation,
            claimbackPeriod: proof.claimbackPeriod,
            claimableBackUnit: proof.claimbackPeriod > 0 ? proof.allocation : '0',
            tgeNumerator: proof.tgeNumerator,
            tgeDenominator: proof.tgeDenominator,
            cliffDuration: proof.cliffDuration,
            cliffNumerator: proof.cliffNumerator,
            cliffDenominator: proof.cliffDenominator,
            vestingPeriodCount: proof.vestingPeriodCount,
            vestingPeriodDuration: proof.vestingPeriodDuration,
            tgeStartTimestamp: 0,
            locked: '0',
            unlocked: '0',
            nextUnlockTimestamp: 0,
            nextUnlockValue: '0',
            tradeable: proof.tradeable as 1 | 2,
            proofs: proof.proofs,
          };

          memberships.push(_membership);
        }
      }
    }

    const presaleContract = getContract({
      client: this.client,
      address: this.presaleContractAddress,
      abi: presaleV5Abi,
    });

    const filtredMemberships = await Promise.all(
      memberships.map(async (membership) => {
        const proofsHash = this.getProofsHash({
          address: walletAddress,
          price: BigInt(membership.price),
          allocation: BigInt(membership.allocation),
          claimbackPeriod: BigInt(membership.claimbackPeriod),
          tgeNumerator: membership.tgeNumerator,
          tgeDenominator: membership.tgeDenominator,
          cliffDuration: membership.cliffDuration,
          cliffNumerator: membership.cliffNumerator,
          cliffDenominator: membership.cliffDenominator,
          vestingPeriodCount: membership.vestingPeriodCount,
          vestingPeriodDuration: membership.vestingPeriodDuration,
          tradeable: membership.tradeable,
        });

        return {
          participated: await presaleContract.read.roundParticipants([
            BigInt(membership.roundId),
            proofsHash,
          ]),
          membership: membership,
        };
      }),
    ).then((results) => {
      return results
        .filter((result) => !result.participated)
        .map((result) => result.membership);
    });

    return filtredMemberships;
  }

  private async getNftMemberships(walletAddress: Address) {
    const memberships: PresaleV5Membership[] = [];

    const membershipNftContract = getContract({
      address: this.membership,
      abi: membershipV5Abi,
      client: this.client,
    });

    // Get NFT memberships
    try {
      const balance = await membershipNftContract.read.balanceOf([walletAddress]) as bigint;

      for (let i = 0; i < Number(balance); i++) {
        const tokenId = await membershipNftContract.read.tokenOfOwnerByIndex([
          walletAddress,
          BigInt(i),
        ]) as bigint;

        const [attributes, usage] = await Promise.all([
          membershipNftContract.read.getAttributes([tokenId]),
          membershipNftContract.read.getUsage([tokenId])
        ]);
        const roundId = await membershipNftContract.read.getRoundId([tokenId]) as bigint;

        const round = this.rounds.find((r) => r.roundId === Number(roundId));

        if (round) {
          const _membership: PresaleV5Membership = {
            id: tokenId.toString(),
            roundId: round.roundId,
            usage: {
              max: usage.max.toString(),
              current: usage.current.toString(),
            },
            price: attributes.price.toString(),
            allocation: attributes.allocation.toString(),
            claimbackPeriod: Number(attributes.claimbackPeriod),
            claimableBackUnit: Number(attributes.claimbackPeriod) > 0 ? usage.max.toString() : '0',
            tgeNumerator: Number(attributes.tgeNumerator),
            tgeDenominator: Number(attributes.tgeDenominator),
            cliffDuration: Number(attributes.cliffDuration),
            cliffNumerator: Number(attributes.cliffNumerator),
            cliffDenominator: Number(attributes.cliffDenominator),
            vestingPeriodCount: Number(attributes.vestingPeriodCount),
            vestingPeriodDuration: Number(attributes.vestingPeriodDuration),
            tgeStartTimestamp: 0,
            locked: '0',
            unlocked: '0',
            nextUnlockTimestamp: 0,
            nextUnlockValue: '0',
            tradeable: Number(attributes.tradeable) as 1 | 2,
            proofs: [],
          };

          memberships.push(_membership);
        }
          console.log("🚀 ~ PresaleV5 ~ getNftMemberships ~ memberships:", memberships)
      }
    } catch (error) {
      console.warn('Failed to fetch NFT memberships:', error);
    }

    return memberships;
  }

  private getProofsHash = ({
    address,
    price,
    allocation,
    claimbackPeriod,
    tgeNumerator,
    tgeDenominator,
    cliffDuration,
    cliffNumerator,
    cliffDenominator,
    vestingPeriodCount,
    vestingPeriodDuration,
    tradeable,
  }: {
    address: Address;
    price: bigint;
    allocation: bigint;
    claimbackPeriod: bigint;
    tgeNumerator: number;
    tgeDenominator: number;
    cliffDuration: number;
    cliffNumerator: number;
    cliffDenominator: number;
    vestingPeriodCount: number;
    vestingPeriodDuration: number;
    tradeable: number;
  }) => {
    const encodedData = encodeAbiParameters(
      [
        {
          name: 'address',
          type: 'address',
        },
        {
          name: 'price',
          type: 'uint256',
        },
        {
          name: 'allocation',
          type: 'uint256',
        },
        {
          name: 'claimbackPeriod',
          type: 'uint256',
        },
        {
          name: 'tgeNumerator',
          type: 'uint32',
        },
        {
          name: 'tgeDenominator',
          type: 'uint32',
        },
        {
          name: 'cliffDuration',
          type: 'uint32',
        },
        {
          name: 'cliffNumerator',
          type: 'uint32',
        },
        {
          name: 'cliffDenominator',
          type: 'uint32',
        },
        {
          name: 'vestingPeriodCount',
          type: 'uint32',
        },
        {
          name: 'vestingPeriodDuration',
          type: 'uint32',
        },
        {
          name: 'tradeable',
          type: 'uint8',
        },
      ],
      [
        address,
        price,
        allocation,
        claimbackPeriod,
        tgeNumerator,
        tgeDenominator,
        cliffDuration,
        cliffNumerator,
        cliffDenominator,
        vestingPeriodCount,
        vestingPeriodDuration,
        tradeable,
      ],
    );

    return keccak256(encodedData);
  };

  public async setApproval(
    walletClient: WalletClient,
    chain: Chain,
    amount: bigint,
  ): Promise<Address> {
    const account = walletClient.account?.address;

    if (!account) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.collectedToken,
      abi: erc20Abi,
      functionName: 'approve',
      args: [this.presaleContractAddress, amount],
      account,
      chain,
    });
  }

  public async transferMembership(
    walletClient: WalletClient,
    chain: Chain,
    to: Address,
    membershipId: string,
  ): Promise<Address> {
    const account = walletClient.account?.address;

    if (!account) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.membership,
      abi: membershipV5Abi,
      functionName: 'transferFrom',
      args: [account, to, BigInt(membershipId)],
      account,
      chain,
    });
  }

  public async claimTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
  ): Promise<Address> {
    const account = walletClient.account?.address;

    if (!account) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV5Abi,
      functionName: 'claim',
      args: [BigInt(membershipId)],
      account,
      chain,
    });
  }

  public async claimBackTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
    amount: bigint,
  ): Promise<Address> {
    const account = walletClient.account?.address;

    if (!account) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV5Abi,
      functionName: 'claimback',
      args: [BigInt(membershipId), amount],
      account,
      chain,
    });
  }

  public async buyTokens(
    walletClient: WalletClient,
    chain: Chain,
    membership: Membership,
    amount: bigint,
  ): Promise<Address> {
    const account = walletClient.account?.address;

    if (!account) {
      throw new Error('Account is undefined');
    }

    if (membership.proofs && membership.proofs.length > 0) {
      return walletClient.writeContract({
        address: this.presaleContractAddress,
        abi: presaleV5Abi,
        functionName: 'buy',
        args: [
          BigInt(membership.roundId),
          amount,
          {
            price: BigInt(membership.price),
            allocation: BigInt(membership.allocation),
            claimbackPeriod: BigInt(membership.claimbackPeriod),
            tgeNumerator: membership.tgeNumerator,
            tgeDenominator: membership.tgeDenominator,
            cliffDuration: membership.cliffDuration,
            cliffNumerator: membership.cliffNumerator,
            cliffDenominator: membership.cliffDenominator,
            vestingPeriodCount: membership.vestingPeriodCount,
            vestingPeriodDuration: membership.vestingPeriodDuration,
            tradeable: (membership as PresaleV5Membership).tradeable,
          },
          membership.proofs.map((proof) => proof as `0x${string}`),
        ],
        account,
        chain,
        // value: EthValue,
      });
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV5Abi,
      functionName: 'extend',
      args: [BigInt(membership.id), amount],
      account,
      chain,
      // value: EthValue,
    });
  }
}
